#include "ImGuiELGS.h"
#include "aimi_icon.h"
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <linux/input.h>
#include <cstdlib>
#include <cstring>
#include <chrono>
#include <thread>
#include <stdio.h>
#include <pthread.h>
#include <android/log.h>
#include <signal.h>
#include <limits.h>
#include "wcnm.h"
#include "sha256.h"
#include "txyun.h"
#include "qd\sr.hpp"
#include "check.h"
#include "CloudData.h"
#define RESET_TEXT "\033[0m"
#define PINK_TEXT "\033[35m"
#define BOLD_TEXT "\033[1m"
c_driver *driver = nullptr;
int output = 2;
int choice = 0;
int mode = 0;
char brief[1024] = {0};
char version[1024] = {0};
char download_url[1024] = {0};
#define CRITICAL_PROTECTION __attribute__((optnone)) __attribute__((noinline))                          \
__attribute__((annotate("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3 " \
                        "bcf bcf_prob=90 bcf_loop=2 bcf_cond_compl=3 bcf_junkasm "                      \
                        "bcf_junkasm_minnum=2 bcf_junkasm_maxnum=4 strenc strenc_prob=100 "             \
                        "sub sub_prob=70 sub_loop=2 split split_num=3")))
#define OBFUSCATE_FULL CRITICAL_PROTECTION

int GetEventCount() CRITICAL_PROTECTION
{
    int count = 0;
    dirent *ptr = NULL;
    DIR *dir = opendir("/dev/input/");
    if (dir == NULL)
    {
        std::cerr << "\033[31;1m[-] Failed to open /dev/input/\033[30;1m" << RESET_TEXT << std::endl;
        return -1;
    }
    while ((ptr = readdir(dir)) != NULL)
    {
        if (strstr(ptr->d_name, "event"))
        {
            count++;
        }
    }
    closedir(dir);
    return count ? count : -1;
}

void RunMainLogic() CRITICAL_PROTECTION
{
    ImGuiELGS *ELGS = new ImGuiELGS();
    if (ELGS != nullptr)
    {
        ELGS->ImGuiGetScreenInformation();
        if (!ELGS->ImGuiGetSurfaceWindow())
        {
            delete ELGS;
            return;
        }
        ELGS->ImGuiInItialization();
    }

    input_event event;
    int eventcount = GetEventCount();
    if (eventcount <= 0)
    {
        std::cerr << "\033[31;1m[-] 获取音量监听失败\033[30;1m" << RESET_TEXT << std::endl;
        delete ELGS;
        return;
    }

    int *volumedevicefilearray = (int *)malloc(eventcount * sizeof(int));
    for (int i = 0; i < eventcount; i++)
    {
        char inputfilepath[128] = "";
        sprintf(inputfilepath, "/dev/input/event%d", i);
        volumedevicefilearray[i] = open(inputfilepath, O_RDWR | O_NONBLOCK);
    }

    ELGS->yeaimi_icon = ELGS->texturereadsfile(yeaimi_icon, sizeof(yeaimi_icon));
    ELGS->noaimi_icon = ELGS->texturereadsfile(noaimi_icon, sizeof(noaimi_icon));
    Timer Thread_Time_Synchronization;
    Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);
    Thread_Time_Synchronization.AotuFPS_init();

    Thread_Time_Synchronization.SetCPUAffinity();
    while (ELGS->ShutImGuiProcess)
    {
        for (int i = 0; i < eventcount; i++)
        {
            memset(&event, 0, sizeof(input_event));
            read(volumedevicefilearray[i], &event, sizeof(event));
            if (event.type == EV_KEY && event.value == 1)
            {
                if (event.code == KEY_VOLUMEUP)
                {
                    ELGS->ImGuiWindowDisplay = true;
                }
                else if (event.code == KEY_VOLUMEDOWN)
                {
                    ELGS->ImGuiWindowDisplay = false;
                }
            }
        }

        // 渲染ImGui窗口
        ELGS->ImGuiWindowStar();
        ELGS->ImGuiWindowMenu();
        ELGS->ImGuiWindowDraw();
        ELGS->ImGuiWindowExit();
        Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);
        Thread_Time_Synchronization.AotuFPS();
    }

    ELGS->ImGuiWindowRele();
    free(volumedevicefilearray);
    delete ELGS;
}
int main(int argc, char *argv[]) CRITICAL_PROTECTION
{
    class SHA256 sha256;
    std::string exePath = SHA256::GetProcExePath();
    if (login())
    {
        if (CloudData::CheckUpdate())
        {
            return 0;
        }
        std::ifstream inputFile("settings.conf");
        if (inputFile.is_open())
        {
            inputFile >> output >> choice;
            inputFile.close();
            std::cout << BOLD_TEXT << PINK_TEXT << "[+] 已加载上次配置！如需修改删除settings.conf" << RESET_TEXT << std::endl;
        }
        else
        {
            std::cout << BOLD_TEXT << PINK_TEXT << "[+] 不懂就都输入1！！" << RESET_TEXT << std::endl;
            std::cout << BOLD_TEXT << PINK_TEXT << "[1]开启防录屏/[2]关闭防录屏 请输入 (1/2): " << RESET_TEXT;
            std::cin >> output;
            std::cout << BOLD_TEXT << PINK_TEXT << "[1]开启无后台/[2]关闭无后台 请输入 (1/2): " << RESET_TEXT;
            std::cin >> choice;
            std::ofstream outputFile("settings.conf");
            if (outputFile.is_open())
            {
                outputFile << output << " " << choice;
                outputFile.close();
            }
        }
        if (choice == 1)
        {
            system("pm list packages | grep -i 'mt' | sed 's/package://' | while read pkg; do am force-stop $pkg; done");
            pid_t pid = fork();
            if (pid > 0)
            {
                exit(0);
            }
            else if (pid < 0)
            {
                return 1;
            }
            if (setsid() < 0)
            {
                return 1;
            }
            freopen("/dev/null", "r", stdin);
            freopen("/dev/null", "w", stdout);
            freopen("/dev/null", "w", stderr);
        }
        if (getTxyunVersionHashUrl(TXYUN_URL, version, brief, download_url))
        {
            if (!exePath.empty())
            {
                std::string hash = sha256.CalculateFileHash(exePath);
                if (hash == brief)
                {
                    qudong();
                    driver = new c_driver();
                }
            }
            else
            {
                exit(0);
            }
        }
        RunMainLogic();
    }
    return 0;
}
