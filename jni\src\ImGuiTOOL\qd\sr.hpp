#ifndef SR_HPP
#define SR_HPP

//#include "kernel_4_14_117.h"
//#include "kernel_4_14_141.h"
//#include "kernel_4_14_180.h"
//#include "kernel_4_14_186.h"
//#include "kernel_4_14_186b.h"
//#include "kernel_4_14_186c.h"
//#include "kernel_4_14_186d.h"
//#include "kernel_4_14_190.h"
//#include "kernel_4_19_113.h"
//#include "kernel_4_19_157.h"
//#include "kernel_4_19_157b.h"
//#include "kernel_4_19_157c.h"
//#include "kernel_4_19_191.h"
//#include "kernel_4_19_191b.h"
//#include "kernel_4_19_191c.h"
//#include "kernel_4_19_81.h"
//#include "kernel_4_9_186.h"
//#include "kernel_5_4.h"
//#include "kernel_5_4b.h"
//#include "kernel_5_4c.h"
//#include "kernel_5_4d.h"
//#include "kernel_5_10.h"
#include "kernel_5_15.h"
#include "kernel_6_1.h"
//#include "kernel_6_6.h"
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <string>
#include <iostream>
#include <vector>
#include <unistd.h>

// 函数声明
inline void write_to_file(const char *filename, const unsigned char *data, unsigned int length);
inline void insmodKernel(const char *path);
inline void load_module(const unsigned char *data, unsigned int length, const char *name);
inline void get_kernel_version(char *version, size_t size);
inline std::string extract_base_version(const std::string& full_version);
inline std::vector<std::string> get_available_versions(const std::string& base_version);
inline void load_driver_version(const std::string& version);
inline void load_matching_drivers(const std::string& kernel_version);
inline void qudong();

// 函数实现
inline void write_to_file(const char *filename, const unsigned char *data, unsigned int length)
{
    FILE *fp = fopen(filename, "wb");
    if (fp == nullptr)
    {
        std::cerr << "无法创建临时文件: " << filename << std::endl;
        exit(EXIT_FAILURE);
    }
    fwrite(data, 1, length, fp);
    fclose(fp);
}

inline void insmodKernel(const char *path)
{
    std::string command = std::string("/system/bin/insmod ") + path + " 2>/dev/null";
    int ret = system(command.c_str());
    if (ret == 0)
    {
        //std::cout << "加载内核驱动成功" << std::endl;
    }
    else
    {
        std::cerr << "加载内核驱动失败" << std::endl;
    }
}

inline void load_module(const unsigned char *data, unsigned int length, const char *name)
{
    const char *temp_file = "/data/local/tmp/nh_temp.ko";
    write_to_file(temp_file, data, length);
    //std::cout << "准备加载内核驱动: " << name << std::endl;
    insmodKernel(temp_file);
    unlink(temp_file);
}

inline void get_kernel_version(char *version, size_t size)
{
    FILE *fp = popen("uname -r", "r");
    if (fp == nullptr)
    {
        std::cerr << "无法执行uname命令获取内核版本" << std::endl;
        exit(EXIT_FAILURE);
    }
    if (fgets(version, size, fp) == nullptr)
    {
        pclose(fp);
        std::cerr << "读取内核版本失败" << std::endl;
        exit(EXIT_FAILURE);
    }
    version[strcspn(version, "\n")] = '\0';
    pclose(fp);
}

inline std::string extract_base_version(const std::string& full_version)
{
    // 第一步：获取主版本号 (x.xx)
    size_t first_dot = full_version.find('.');
    if (first_dot == std::string::npos) return full_version;
    
    size_t second_dot = full_version.find('.', first_dot + 1);
    if (second_dot == std::string::npos) return full_version;
    
    std::string main_version = full_version.substr(0, second_dot);
    
    // 第二步：对于4.14和4.19系列，继续匹配第三段版本号
    if (main_version == "4.14" || main_version == "4.19") {
        size_t third_dot = full_version.find('.', second_dot + 1);
        size_t end_pos = third_dot;
        
        // 如果没有第三个点，找到第一个非数字字符
        if (third_dot == std::string::npos) {
            end_pos = full_version.find_first_not_of("0123456789", second_dot + 1);
            if (end_pos == std::string::npos) {
                end_pos = full_version.length();
            }
        }
        
        return full_version.substr(0, end_pos);
    }
    
    // 对于其他版本（如5.15），返回主版本号
    return main_version;
}

inline std::vector<std::string> get_available_versions(const std::string& base_version)
{
    std::vector<std::string> versions;
    
    if (base_version == "4.9") {
        versions = {"4.9.186"};
    } 
    else if (base_version == "4.14") {
        versions = {"4.14.117", "4.14.180", "4.14.186", "4.14.186b", "4.14.186c"};
    }
    else if (base_version == "4.19") {
        versions = {"4.19.81", "4.19.113", "4.19.157", "4.19.157b", "4.19.157c", 
                   "4.19.191", "4.19.191b", "4.19.191c"};
    }
    else if (base_version == "5.4") {
        versions = {"5.4", "5.4b", "5.4c", "5.4d"};
    }
    else if (base_version == "5.10") {
        versions = {"5.10"};
    }
    else if (base_version == "5.15") {
        versions = {"5.15"};
    }
    else if (base_version == "6.1") {
        versions = {"6.1"};
    }
    else if (base_version == "6.6") {
        versions = {"6.6"};
    }
    
    return versions;
}

inline void load_driver_version(const std::string& version)
{
    if (version == "4.9.186") {
        //load_module(kernel_4_9_186, sizeof(kernel_4_9_186), version.c_str());
    }
    else if (version == "4.14.117") {
        //load_module(kernel_4_14_117, sizeof(kernel_4_14_117), version.c_str());
    }
    else if (version == "4.14.180") {
        //load_module(kernel_4_14_180, sizeof(kernel_4_14_180), version.c_str());
    }
    else if (version == "4.14.186") {
        //load_module(kernel_4_14_186, sizeof(kernel_4_14_186), version.c_str());
    }
    else if (version == "4.14.186b") {
        //load_module(kernel_4_14_186b, sizeof(kernel_4_14_186b), version.c_str());
    }
    else if (version == "4.14.186c") {
        //load_module(kernel_4_14_186c, sizeof(kernel_4_14_186c), version.c_str());
    }
    else if (version == "4.19.81") {
        //load_module(kernel_4_19_81, sizeof(kernel_4_19_81), version.c_str());
    }
    else if (version == "4.19.113") {
        //load_module(kernel_4_19_113, sizeof(kernel_4_19_113), version.c_str());
    }
    else if (version == "4.19.157") {
        //load_module(kernel_4_19_157, sizeof(kernel_4_19_157), version.c_str());
    }
    else if (version == "4.19.157b") {
        //load_module(kernel_4_19_157b, sizeof(kernel_4_19_157b), version.c_str());
    }
    else if (version == "4.19.157c") {
        //load_module(kernel_4_19_157c, sizeof(kernel_4_19_157c), version.c_str());
    }
    else if (version == "4.19.191") {
        //load_module(kernel_4_19_191, sizeof(kernel_4_19_191), version.c_str());
    }
    else if (version == "4.19.191b") {
        //load_module(kernel_4_19_191b, sizeof(kernel_4_19_191b), version.c_str());
    }
    else if (version == "4.19.191c") {
        //load_module(kernel_4_19_191c, sizeof(kernel_4_19_191c), version.c_str());
    }
    else if (version == "5.4") {
        //load_module(kernel_5_4, sizeof(kernel_5_4), version.c_str());
    }
    else if (version == "5.4b") {
        //load_module(kernel_5_4b, sizeof(kernel_5_4b), version.c_str());
    }
    else if (version == "5.4c") {
        //load_module(kernel_5_4c, sizeof(kernel_5_4c), version.c_str());
    }
    else if (version == "5.4d") {
        //load_module(kernel_5_4d, sizeof(kernel_5_4d), version.c_str());
    }
    else if (version == "5.10") {
        //load_module(kernel_5_10, sizeof(kernel_5_10), version.c_str());
    }
    else if (version == "5.15") {
        load_module(kernel_5_15, sizeof(kernel_5_15), version.c_str());
    }
    else if (version == "6.1") {
        load_module(kernel_6_1, sizeof(kernel_6_1), version.c_str());
    }
    else if (version == "6.6") {
        //load_module(kernel_6_6, sizeof(kernel_6_6), version.c_str());
    }
    else {
        std::cerr << "错误: 未知的驱动版本 " << version << std::endl;
    }
}

inline void load_matching_drivers(const std::string& kernel_version)
{
    // 1. 提取版本信息
    std::string full_version = extract_base_version(kernel_version);
    std::string base_ver = full_version.substr(0, full_version.find('.', full_version.find('.') + 1));
    
    // 2. 获取可用驱动版本
    std::vector<std::string> available_versions = get_available_versions(base_ver);
    if (available_versions.empty()) {
        std::cerr << "错误: 未找到适用于内核版本 " << kernel_version << " 的驱动" << std::endl;
        return;
    }  
    // 3. 查找匹配版本
    std::string target_version = full_version;
    size_t non_digit = target_version.find_first_not_of("0123456789.");
    if (non_digit != std::string::npos) {
        target_version = target_version.substr(0, non_digit);
    }
    
    std::vector<std::string> matches;
    for (const auto& ver : available_versions) {
        if (ver.find(target_version) == 0) {
            matches.push_back(ver);
        }
    }
    
    // 4. 处理匹配结果
    const std::vector<std::string>& versions_to_show = matches.empty() ? available_versions : matches;
    
    // 如果只有一个版本，直接加载
    if (versions_to_show.size() == 1) {
       // std::cout << "加载驱动: " << versions_to_show[0] << std::endl;
        load_driver_version(versions_to_show[0]);
        return;
    }
    
    // 显示可用版本
    std::cout << "可用的驱动版本:" << std::endl;
    for (size_t i = 0; i < versions_to_show.size(); ++i) {
        std::cout << "[" << i+1 << "] " << versions_to_show[i] << std::endl;
    }
    
    // 获取用户选择
    int choice = 0;
    std::cout << "请选择要加载的驱动版本 (1-" << versions_to_show.size() << "): ";
    if (!(std::cin >> choice) || choice < 1 || choice > static_cast<int>(versions_to_show.size())) {
        std::cerr << "错误: 无效的选择，退出程序" << std::endl;
        exit(EXIT_FAILURE);
    }
    
    // 加载选中的驱动
    //std::cout << "加载驱动: " << versions_to_show[choice-1] << std::endl;
    load_driver_version(versions_to_show[choice-1]);
}

inline void qudong()
{
    if (geteuid() != 0)
    {
        std::cerr << "错误: 需要ROOT权限运行" << std::endl;
        std::cerr << "请使用su或sudo运行此程序" << std::endl;
        exit(EXIT_FAILURE);
    }

    char kernel_version[128];
    get_kernel_version(kernel_version, sizeof(kernel_version));
   // std::cout << "当前内核: " << kernel_version << std::endl;

    load_matching_drivers(kernel_version);
}

#endif // SR_HPP