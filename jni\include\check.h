#include "t3data/t3data.h"
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <string>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>

int intervalInSeconds = 10000; // 心跳验证间隔
std::string card_kami;		   // 卡密
std::string heartbeat_code;	   // 心跳状态码

bool is_running = false;       // 倒计时运行状态
std::thread countdown_thread;  // 倒计时线程
char buffer[128];              // 存储剩余时间字符串

// 获取蓝牙地址作为设备码的函数
std::string getBluetoothAddress()
{
	FILE *pipe = popen("settings get secure bluetooth_address", "r");
	if (!pipe)
	{
		exit(1);
	}
	char buffer[128];
	std::string result = "";
	if (fgets(buffer, sizeof(buffer), pipe) != nullptr)
	{
		result = buffer;
		if (!result.empty() && result.back() == '\n')
		{
			result.pop_back();
		}
	}
	pclose(pipe);
	if (result.empty() || result == "null")
	{
		exit(1);
	}

	return result;
}

std::string machine_code = getBluetoothAddress(); // 设备码 - 使用蓝牙地址
std::string value_id = "3250";					  // 后台创建一个变量的ID
std::string value_name = "Request";				  // 后台创建一个变量的名称
std::string loacl_version = "1000";				  // 程序当前版本版本号

// 保存卡密到.ka文件
void saveCardKami(const std::string &kami)
{
	std::ofstream file(".ka");
	if (file.is_open())
	{
		file << kami;
		file.close();
	}
}

// 从.ka文件读取卡密
std::string loadCardKami()
{
	std::ifstream file(".ka");
	std::string kami = "";
	if (file.is_open())
	{
		std::getline(file, kami);
		file.close();
	}
	return kami;
}

// 删除卡密文件
void deleteCardKamiFile()
{
	remove(".ka");
}

// 启动倒计时
void startCountdown(long total_seconds)
{
	is_running = true;
	countdown_thread = std::thread([total_seconds]()
								   {
		long remaining = total_seconds;
		while (remaining > 0 && is_running) {
			long days = remaining / (24 * 3600);
			long hours = (remaining % (24 * 3600)) / 3600;
			long minutes = (remaining % 3600) / 60;
			long seconds = remaining % 60;
			sprintf(buffer, "%ld天 %ld时 %ld分 %ld秒", days, hours, minutes, seconds);

			std::this_thread::sleep_for(std::chrono::seconds(1));
			remaining--;
		}
		if (remaining <= 0) {
			sprintf(buffer, "已到期");
			std::cout << "时间到期，程序退出" << std::endl;
			exit(0);
		} });
	countdown_thread.detach();
}



void getValue()
{
	T3DATA getValueApi;										   // 创建一个客户端接口实例对象
	getValueApi.setRequestApi(T3Config::Path_GetValueContent); // 设置接口请求的地址的调用码
	getValueApi.addRequestParam("kami", card_kami);			   // 传入卡密参数
	getValueApi.addRequestParam("valueid", value_id);		   // 传入变量ID
	getValueApi.addRequestParam("valuename", value_name);	   // 传入变量名称
	bool responseResult = getValueApi.sendRequest();
	if (!responseResult)
	{
		return;
	}
	try
	{
		Json::Value responseJson = getValueApi.getResponseJsonObject(); // 获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200)
		{
			std::string responseMessage = responseJson["msg"].asString();
			return;
		}
		if (!getValueApi.requestSafeCodeVerify())
		{
			return;
		}
		if (!getValueApi.requestDataTimeDifferenceVerify())
		{
			return;
		}
		std::string responseMessage = responseJson["msg"].asString();
	}
	catch (const std::exception &ex)
	{
		// std::cout << "处理失败，出现异常" << std::endl;
		return;
	}
}

void heartbeat()
{
	T3DATA heartbeatApi;											// 创建一个客户端接口实例对象
	heartbeatApi.setRequestApi(T3Config::Path_IsSingleLoginStatus); // 设置接口请求的地址的调用码
	heartbeatApi.addRequestParam("kami", card_kami);				// 传入卡密参数
	heartbeatApi.addRequestParam("statecode", heartbeat_code);		// 传入心跳状态码
	bool responseResult = heartbeatApi.sendRequest();
	if (!responseResult)
	{ // 请求处理失败
		// std::cout << "请求失败" << std::endl;
		return;
	}
	try
	{
		Json::Value responseJson = heartbeatApi.getResponseJsonObject(); // 获取响应json对象
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200)
		{
			std::string responseMessage = responseJson["msg"].asString();
			return;
		}
		if (!heartbeatApi.requestSafeCodeVerify())
		{
			return;
		}
		if (!heartbeatApi.requestDataTimeDifferenceVerify())
		{
			return;
		}
		std::string responseMessage = responseJson["msg"].asString();
		if (responseMessage == "心跳验证成功" || responseMessage.rfind("心跳验证成功:", 0) == 0)
		{
		}
		else
		{
			// std::cout << "心跳验证失败，应当结束进程" << std::endl;
			// std::cout << responseMessage << std::endl;
		}
	}
	catch (const std::exception &ex)
	{
		return;
	}
}

// 单码登录方法
bool login()
{
	// 先尝试从文件读取卡密
	card_kami = loadCardKami();
	if (card_kami.empty())
	{
		std::cout << "卡密: ";
		std::getline(std::cin, card_kami);
	}
	else
	{
		std::cout << "卡密: " << card_kami << std::endl;
	}
	T3DATA cardLoginApi;									// 创建一个客户端接口实例对象
	cardLoginApi.setRequestApi(T3Config::Path_SingleLogin); // 设置接口请求的地址的调用码
	cardLoginApi.addRequestParam("kami", card_kami);		// 传入卡密参数
	cardLoginApi.addRequestParam("imei", machine_code);		// 传入机器码参数
	bool responseResult = cardLoginApi.sendRequest();
	if (!responseResult)
	{
		deleteCardKamiFile();
		return false;
	}
	try
	{
		Json::Value responseJson = cardLoginApi.getResponseJsonObject();
		int responseCode = responseJson["code"].asInt();
		if (responseCode != 200)
		{
			std::string responseMessage = responseJson["msg"].asString();
			std::cout << "验证失败：" << responseMessage << std::endl;
			deleteCardKamiFile();
			return false;
		}
		if (!cardLoginApi.requestDataSignatureVerify())
		{
			deleteCardKamiFile();
			return false;
		}
		if (!cardLoginApi.requestSafeCodeVerify())
		{
			deleteCardKamiFile();
			return false;
		}
		if (!cardLoginApi.requestDataTimeDifferenceVerify())
		{
			deleteCardKamiFile();
			return false;
		}
		long expireTimestamp = responseJson["end_time"].asInt64();
		heartbeat_code = responseJson["statecode"].asString();

		long currentTime = time(nullptr);
		long time_diff = expireTimestamp - currentTime;

		if (time_diff <= 0) {
			std::cout << "卡密已到期" << std::endl;
			return false;
		} else {
			long days = time_diff / (24 * 3600);
			long hours = (time_diff % (24 * 3600)) / 3600;
			long minutes = (time_diff % 3600) / 60;
			long seconds = time_diff % 60;
			sprintf(buffer, "%ld天 %ld时 %ld分 %ld秒", days, hours, minutes, seconds);
			std::cout << "剩余时间：" << buffer << std::endl;
			startCountdown(time_diff);
		}
		saveCardKami(card_kami);
		getValue();
		std::thread heartbeatThread([]()
									{
		while (true)
			{
				heartbeat();
				std::this_thread::sleep_for(std::chrono::seconds(intervalInSeconds));
			} });
		heartbeatThread.detach();
		return true;
	}
	catch (const std::exception &ex)
	{
		deleteCardKamiFile();
		return false;
	}
}
